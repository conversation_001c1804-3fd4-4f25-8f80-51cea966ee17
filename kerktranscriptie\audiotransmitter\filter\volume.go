package filter

import (
	"log"

	"github.com/WesotronicAV/kerktranscriptie/audiotransmitter/utils"
	"github.com/go-audio/audio"
)

type VolumeFilter struct {
	minVolumeDb float64
}

func NewVolumeFilter(minVolume float64) *VolumeFilter {
	return &VolumeFilter{minVolume}
}

func (f *VolumeFilter) Apply(input *audio.PCMBuffer) *audio.PCMBuffer {
	volume := utils.CalculateDecibel(utils.CalculateRMS16(input.AsI16()))
	if volume < f.minVolumeDb {
		log.Printf("VolumeFilter: volume too low (%fdb), skipping...", volume)
		return nil
	}
	log.Printf("VolumeFilter: volume is good (%fdb), sending through...", volume)
	return input
}