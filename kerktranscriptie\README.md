This repository holds all code related to kerktranscriptie, a service to transcribe church services and display them on a web app.

## Installation on a Raspberry Pi

- Install Raspberry Pi OS Lite on a Pi
- Connect it to the internet and ssh into the Pi
- Test audio quality `arecord -D plughw:1,0 -f cd -t wav -d 10 -r 44100 test.wav`
- create deploy key if needed (instructions here: https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent#generating-a-new-ssh-key)
- Copy install.sh on the pi and make it executable by `chmod +x install.sh`
- <PERSON><PERSON> deploy key when asked

Open the webpage on the IP at port 5000

## How to update

Will update automatically at every boot

## How to make available outside

Open port 5000, 5001 and 9222
