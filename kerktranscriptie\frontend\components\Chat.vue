<template>
  <v-container height="100vh">
    <v-infinite-scroll side="start">
      <v-col cols="12" v-for="message in messages" :key="message.timestamp">
        <v-card color="indigo-darken-4" :text="message.content"> </v-card>
      </v-col>
    </v-infinite-scroll>
  </v-container>
</template>

<script setup>
import { connect, StringCodec } from "nats.ws";

const languageStore = useLanguageStore();

const messages = ref([]);
let natsConnection;
let subscription;

watch(
  () => messages,
  async (_, _p) => {
    await nextTick();
    window.scrollTo(0, document.body.scrollHeight);
  },
  {
    deep: true,
  }
);

const connectToNATS = async () => {
  // Clean up any existing subscription
  if (subscription) {
    subscription.unsubscribe();
  }
  if (!natsConnection) {
    natsConnection = await connect({
      servers: `ws://${location.hostname}:9222`,
    });
  }

  const subject = await $fetch(
    `http://${location.hostname}:5001/subscriptions`,
    {
      method: "POST",
      body: {
        language: languageStore.language.code,
      },
    }
  );

  const sc = StringCodec();
  subscription = natsConnection.subscribe(subject);
  (async () => {
    console.log(subscription);
    for await (const m of subscription) {
      // Respond to aliveness check
      if (m.respond(sc.encode("OK"))) {
        continue;
      }

      const data = JSON.parse(sc.decode(m.data));
      const index = messages.value.findIndex((i) => i.id === data.id);

      if (index === -1) {
        messages.value.push({
          timestamp: Date.now(),
          id: data.id,
          content: data.content,
        });
      } else {
        messages.value[index].content = data.content;
      }
    }
    console.log("subscription closed");
  })();
};

// Watch for language changes and reconnect
watch(
  () => languageStore.language,
  async () => {
    await connectToNATS();
  }
);

onMounted(() => {
  connectToNATS();
});

onUnmounted(() => {
  if (subscription) subscription.unsubscribe();
  if (natsConnection) natsConnection.close();
});
</script>
