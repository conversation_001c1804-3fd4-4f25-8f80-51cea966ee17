package internal

import (
	"encoding/csv"
	"fmt"
	"io/fs"
	"log"
	"os"
	"os/exec"
)

type Writer struct {
	oggFilePath string
	tmpFilePath string
	csvFiles map[string]*os.File
}

func NewWriter(oggFilePath string) (*Writer, error) {
	wavFile, err := os.Create(oggFilePath)
	wavFile.Close()
	if err != nil {
		return nil, err
	}


	tmpFilePath := fmt.Sprintf("audio/%s.tmp", oggFilePath)

	f, err := os.Create(tmpFilePath); if err != nil {
		return nil, err
	}

	defer f.Close()

	return &Writer{
		oggFilePath: fmt.Sprintf("audio/%s.ogg", oggFilePath),
		tmpFilePath: tmpFilePath,
	}, nil
}

func (w *Writer) WritePCM(data []byte) error {
	f, err := os.OpenFile(w.tmpFilePath, os.O_APPEND|os.O_WRONLY, fs.ModeAppend); if err != nil {
		return err
	}

	defer f.Close()

	_, err = f.Write(data); if err != nil {
		return err
	}

	return nil
}

func (w *Writer) convertToOgg(originalFile, outputFile string) error {
	// Start FFmpeg as a subprocess
	cmd := exec.Command("ffmpeg",
		"-f", "s16le",            // Raw PCM format (signed 16-bit little-endian)
		"-ar", fmt.Sprint(16000), // Sample rate
		"-ac", fmt.Sprint(2),     // Number of channels
		"-i", originalFile,       // Read from stdin
		"-c:a", "libopus",        // Encode to Opus
		"-b:a", "36k",            // Bitrate
		"-v", "quiet",            // Don't show progress
		"-y",                     // Overwrite output file
		outputFile,               // Output file
	)

	cmd.Stderr = os.Stderr // Show errors in the terminal
	cmd.Stdout = nil	   // Discard output

	return cmd.Run()
}

func (w *Writer) WriteText(filename, text string) error {
	f, err := os.OpenFile(filename, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644); if err != nil {
		return err
	}

	defer f.Close()

	_, err = f.WriteString(text); if err != nil {
		return err
	}

	return nil
}

func (w *Writer) WriteCsv(language string, record []string) error {
	if _, ok := w.csvFiles[language]; !ok {
		csvFile, err := os.OpenFile(language, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644); if err != nil {
			return err
		}
		w.csvFiles[language] = csvFile
	}

	writer := csv.NewWriter(w.csvFiles[language])
	defer writer.Flush()

	return writer.Write(record)
}

func (w *Writer) Close() {
	for _, file := range w.csvFiles {
		if err := file.Close(); err != nil {
			log.Printf("Error closing file: %s", err)
		}
	}
}