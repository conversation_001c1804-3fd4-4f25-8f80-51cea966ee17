package filter

import (
	"fmt"
	"log"
	"os"

	"github.com/go-audio/audio"
	"github.com/go-audio/wav"
)

type FileFilter struct {
	filePath string
	file *os.File
	encoder *wav.Encoder
}

func NewFileFilter(filePath string) *FileFilter {
	file, err := os.Create(fmt.Sprintf("audio/%s.wav", filePath)); if err != nil {
		log.Fatal(err)
	}
	file.Chmod(os.ModePerm)
	encoder := wav.NewEncoder(file, 16000, 16, 2, 1)
	defer encoder.Close()
	return &FileFilter{filePath: filePath}
}

func (f *FileFilter) Apply(input *audio.PCMBuffer) *audio.PCMBuffer {
	if err := f.encoder.Write(input.AsIntBuffer()); err != nil {
		log.Println(fmt.Errorf("encoder write buffer: %w", err))
	}
	return input
}

func (f *FileFilter) Close() {
	f.encoder.Close()
	f.file.Close()
}