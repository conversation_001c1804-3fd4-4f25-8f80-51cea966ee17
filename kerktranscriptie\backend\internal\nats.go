package internal

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/WesotronicAV/kerktranscriptie/common"
	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
)

type subject struct {
	subject string
	language string
}

type Nats struct {
	backend *Backend
	audioSubscription *nats.Subscription
	conn *nats.Conn
	subjects map[string][]subject
	csvLogFileName string
	languages map[string]Language
	sync.Mutex
}

func NewNats(b *Backend, conn *nats.Conn) (*Nats, error) {
	fn := fmt.Sprintf("audio/%d.csv", time.Now().Unix())

	languages, err := LoadLanguages("languages.json"); if err != nil {
		return nil, err
	}

	n := &Nats{
		backend: b,
		conn: conn,
		csvLogFileName: fn,
		languages: languages,
	}
	go func() {
		for {
			n.checkSubscriptions()
		}
	}()
	n.listen()
	return n, err
}

func (n *Nats) listen() error {
	audioSubject := n.backend.SettingsHolder.AudioTransmitterToken.String()
	audioSubscription, err := n.conn.Subscribe(audioSubject, n.HandleMessage); if err != nil {
		fmt.Printf("Subscription to %s failed: %s\n", audioSubject, err)
		return nil
	}
	n.audioSubscription = audioSubscription
	return nil
}

func (n *Nats) HandleMessage(msg *nats.Msg) {
	data := common.ConvertByteSliceToInt16(msg.Data)

	// Only transcribe if there are clients listening
	if len(n.subjects) > 0 {
		n.backend.Prompt.Transcribe(data)
	}

	if n.backend.SettingsHolder.Settings.SaveFile {
		err := n.backend.Writer.WritePCM(msg.Data); if err != nil {
			fmt.Printf("Writing to ogg file failed: %s\n", err)
		}
	}
}

func (n *Nats) AddListener(language string) (string, error) {
	// Lock to prevent issues with subscription check
	n.Lock()
	defer n.Unlock()

	s := uuid.New().String()

	if _, ok := n.subjects[language]; !ok {
		n.subjects[language] = []subject{}
	}
	n.subjects[language] = append(n.subjects[language], subject{subject: s, language: language})
	return s, nil
}

func (n *Nats) Publish(m Message) {
	originalTimestamp := time.Now().Unix()
	nativeLanguageSubjects, ok := n.subjects[n.backend.SettingsHolder.Settings.Language]; if ok {
		for _, s := range nativeLanguageSubjects {
				n.publish(s.subject, m)
		}
	}

	originalText := m.Content
	for lang, subjects := range n.subjects {
		if lang == n.backend.SettingsHolder.Settings.Language {
			continue
		}
		if m.Completed {		
			go func ()  {
				res, err := n.backend.Prompt.Translate(originalText, n.languages[lang].Name); if err != nil {
					fmt.Printf("Translation failed: %s\n", err)
				}
				m.Content = res
				if (n.backend.SettingsHolder.Settings.SaveTranslationCsvLog) {
					n.appendToFile(originalTimestamp, originalText, time.Now().Unix(), res)
				}
				for _, s := range subjects {
					n.publish(s.subject, m)
				}
			}()
		}
	}
}

func (n *Nats) Close() {
	n.conn.Drain()
}

func (n *Nats) publish(s string, m Message) {
	data, err := json.Marshal(m); if err != nil {
		fmt.Printf("Marshalling message failed: %s\n", err)
		return
	}
	err = n.conn.Publish(s, data); if err != nil {
		fmt.Printf("Publishing to %s failed: %s\n", s, err)
	}
}

func (n *Nats) checkSubscriptions() {
	// Lock to prevent issues with addition of new subscriptions
	n.Lock()

	_, failedSubscriptions := n.checkFailedSubscriptions()
	n.subjects = n.filterFailedSubscriptions(failedSubscriptions)

	n.Unlock()

	time.Sleep(5 * time.Second)
}

func (n *Nats) checkFailedSubscriptions() (int, map[string]map[string]bool) {
	totalFailedSubscriptions := 0
	failedSubscriptions := map[string]map[string]bool{}

	for lang, subjects := range n.subjects {
		failedLangSubscriptions := map[string]bool{}
		for _, s := range subjects {
			_, err := n.conn.Request(s.subject, []byte("ping"), 5*time.Second)
			if err != nil {
				fmt.Printf("Subscription %s failed: %s\n", s.subject, err)
				failedLangSubscriptions[s.subject] = true
				totalFailedSubscriptions++
			}
		}
		failedSubscriptions[lang] = failedLangSubscriptions
	}

	return totalFailedSubscriptions, failedSubscriptions
}

func (n *Nats) filterFailedSubscriptions(failedSubscriptions map[string]map[string]bool) map[string][]subject {
	newLangSubjects := make(map[string][]subject)

	for lang, subjects := range n.subjects {
		newSubjects := []subject{}
		for _, s := range subjects {
			if _, ok := failedSubscriptions[lang][s.subject]; !ok {
				newSubjects = append(newSubjects, s)
			}
		}
		if len(newSubjects) != 0 {
			newLangSubjects[lang] = newSubjects
		}
	}

	return newLangSubjects
}

func (n *Nats) appendToFile(originalTimestamp int64, originalText string, translatedTimestamp int64, translatedText string) {
	// Convert timestamps to Dutch format: DD-MM-YYYY HH:mm:ss
	originalTime := time.Unix(originalTimestamp, 0).Format("02-01-2006 15:04:05")
	translatedTime := time.Unix(translatedTimestamp, 0).Format("02-01-2006 15:04:05")

	// Open or create the CSV file in append mode
	file, err := os.OpenFile(n.csvLogFileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Printf("Error opening file: %s\n", err)
		return
	}
	defer file.Close()

	// Create a CSV writer
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write the data to the CSV file
	record := []string{originalTime, originalText, translatedTime, translatedText}
	if err := writer.Write(record); err != nil {
		fmt.Printf("Error writing to file: %s\n", err)
	}
}
