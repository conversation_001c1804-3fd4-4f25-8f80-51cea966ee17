package internal

import (
	"fmt"
	"io"
	"strings"

	"github.com/Azure/azure-sdk-for-go/sdk/ai/azopenai"
	"github.com/go-audio/audio"
	"github.com/go-audio/wav"
	"github.com/orcaman/writerseeker"
)

type Prompt struct {
	backend *Backend
	previousText string
	messages []azopenai.ChatRequestMessageClassification
	buf []int16
	Message Message
}

func NewPrompt(b *Backend) *Prompt {
	return &Prompt{
		backend: b,
		Message: NewMessage(),
	}
}

func (p *Prompt) Transcribe(buf []int16) {
	p.buf = buf

	p.previousText = p.backend.OpenAI.handlePrompt(*p)
	p.Message = NewMessage()
}

func (p *Prompt) Translate(text string, language string) (string, error) {
	return p.backend.OpenAI.Translate(text, language)
}

func (p *Prompt) toString(commonWords []string) string {
	var sb strings.Builder
	sb.WriteString("Veelgebruikte woorden: ")
	for _, w := range commonWords {
		sb.WriteString(w)
		sb.WriteString(", ")
	}
	sb.WriteString(". ")
	return sb.String()
}

func (p *Prompt) toWav() ([]byte, error) {
	buf := &writerseeker.WriterSeeker{}

	soundIntBuffer := &audio.PCMBuffer{
		Format: &audio.Format{SampleRate: 16000, NumChannels: 2},
		DataType: audio.DataTypeI16,
		I16: p.buf,
	}

	encoder := wav.NewEncoder(buf, 16000, 16, 2, 1)
	defer encoder.Close()
	
	if err := encoder.Write(soundIntBuffer.AsIntBuffer()); err != nil {
		fmt.Println(fmt.Errorf("encoder write buffer: %w", err))
	}

	encoder.Close()

	return io.ReadAll(buf.Reader())
}
