<template>
  <v-speed-dial
    location="bottom end"
    transition="fade-transition"
    :close-on-content-click="false"
  >
    <template v-slot:activator="{ props: activatorProps }">
      <v-fab
        id="button"
        color="primary"
        v-bind="activatorProps"
        icon="mdi-human"
        location="bottom end"
        :size="buttonSize"
        fixed
        app
        appear
        @touchstart="buttonHover"
        @mouseover="buttonHover"
        @mouseleave="buttonHoverStop"
        @touchend="buttonHoverStop"
      ></v-fab>
    </template>

    <v-btn
      key="1"
      icon="mdi-format-font-size-increase"
      @click="sizeUp"
      size="4rem"
    ></v-btn>
    <v-btn
      key="2"
      icon="mdi-format-font-size-decrease"
      @click="sizeDown"
      size="4rem"
    ></v-btn>
  </v-speed-dial>
</template>

<script setup>
const buttonSize = ref("4rem");
const fontsize = ref(1);

function buttonHover() {
  buttonSize.value = "4rem";
}

function buttonHoverStop() {
  setTimeout(() => {
    buttonSize.value = "2rem";
  }, 2000);
}

function sizeUp() {
  fontsize.value = fontsize.value + 0.2;
  document.documentElement.style.setProperty(
    "font-size",
    `${fontsize.value}rem`
  );
}

function sizeDown() {
  fontsize.value = fontsize.value - 0.2;
  document.documentElement.style.setProperty(
    "font-size",
    `${fontsize.value}rem`
  );
}

onMounted(() => {
  setTimeout(() => {
    buttonSize.value = "2rem";
  }, 2000);
});
</script>
