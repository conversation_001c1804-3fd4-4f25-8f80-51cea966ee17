package utils

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gordonklaus/portaudio"
)

type Config struct {
	path string
	Microphone *portaudio.DeviceInfo
	MinimumVolume float64
	MinimumSegmentVolumeDb float64
	MinimumSegment time.Duration
	MaximumSegment time.Duration
	EnableVad bool
	SaveFile bool
	AudioTransmitterToken string
	DevMode bool
}

func NewConfig(path string) (*Config, error) {
	c := &Config{path: path}

	err := c.ReloadConfig(); if err != nil {
		return nil, err
	}

	return c, nil
}

func (c *Config) ReloadConfig() error {
	f, err := os.Open(c.path); if err != nil {
		return err
	}

	s := bufio.NewScanner(f)

	for s.Scan() {
		line := s.Text()

		splitted := strings.SplitN(line, "=", 2)
		if splitted[0] == "ALSA_INPUT" {
			c.Microphone, err = selectInputDevice(splitted[1]); if err != nil {
				return err
			}
		}

		if splitted[0] == "MIN_MIC_VOLUME" {
			minVolInt, err := strconv.Atoi(splitted[1]); if err != nil {
				return err
			}

			c.MinimumVolume = float64(minVolInt)
		}

		if splitted[0] == "MIN_SEG_VOLUME_DB" {
			minSegVolInt, err := strconv.Atoi(splitted[1]); if err != nil {
				return err
			}

			c.MinimumSegmentVolumeDb = float64(minSegVolInt)
		}

		if splitted[0] == "MIN_STEP_S" {
			minSegment, err := strconv.Atoi(splitted[1]); if err != nil {
				return err
			}

			c.MinimumSegment = time.Second * time.Duration(minSegment)
		}

		if splitted[0] == "MAX_STEP_S" {
			maxSegment, err := strconv.Atoi(splitted[1]); if err != nil {
				return err
			}

			c.MaximumSegment = time.Second * time.Duration(maxSegment)
		}

		if splitted[0] == "ENABLE_VAD" {
			enableVad, err := strconv.ParseBool(splitted[1]); if err != nil {
				return err
			}

			c.EnableVad = enableVad
		}

		if splitted[0] == "SAVE_FILE" {
			saveFile, err := strconv.ParseBool(splitted[1]); if err != nil {
				return err
			}

			c.SaveFile = saveFile
		}

		if splitted[0] == "AUDIOTRANSMITTER_TOKEN" {
			c.AudioTransmitterToken = splitted[1]
		}

		if splitted[0] == "DEV_MODE" {
			devMode, err := strconv.ParseBool(splitted[1]); if err != nil {
				return err
			}

			c.DevMode = devMode
		}
	}

	return nil
}

func (c *Config) PrintConfig() {
	log.Println("")
    log.Println("------------------ WESOTRONIC ------------------")
    log.Println("")
    log.Println("Selected device:", c.Microphone.Name, c.Microphone.DefaultSampleRate)
    log.Println("Minimum segment length:", c.MinimumSegment)
    log.Println("Maximum segment length:", c.MaximumSegment)
    log.Println("Minimum microphone volume:", c.MinimumVolume)
    log.Println("Enable VAD:", c.EnableVad)
    log.Println("Save to file:", c.SaveFile)
    log.Println("")
    log.Println("------------------ WESOTRONIC ------------------")
    log.Println("")  
}

func selectInputDevice(deviceName string) (*portaudio.DeviceInfo, error) {
	devices, err := portaudio.Devices()
	if err != nil {
		return nil, fmt.Errorf("select input device %w", err)
	}

	selectedDevice := devices[0]

	for _, device := range devices {
		fmt.Printf("%s: %s\n", deviceName, device.Name)
		if strings.Contains(device.Name, deviceName) {
			selectedDevice = device
			break
		}
	}

	return selectedDevice, nil
}