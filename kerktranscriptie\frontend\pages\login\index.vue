<template>
  <v-container class="h-screen" v-if="!loggedIn">
    <v-row class="h-100">
      <v-col class="align-self-center">
        <v-img
          class="mx-auto my-6"
          max-width="228"
          src="https://wesotronic.nl/wp-content/themes/utrongroup/img/logo-wesotronic.svg"
        ></v-img>

        <v-card
          class="mx-auto pa-12 pb-8"
          elevation="8"
          max-width="448"
          rounded="lg"
        >
          <v-alert
            class="mb-5"
            density="compact"
            title="Onjuist wachtwoord"
            type="error"
            v-if="error"
          ></v-alert>
          <div
            class="text-subtitle-1 text-medium-emphasis d-flex align-center justify-space-between"
          >
            Admin wachtwoord
          </div>

          <v-text-field
            :append-inner-icon="visible ? 'mdi-eye-off' : 'mdi-eye'"
            :type="visible ? 'text' : 'password'"
            density="compact"
            placeholder="Voer uw wachtwoord in"
            prepend-inner-icon="mdi-lock-outline"
            variant="outlined"
            @click:append-inner="visible = !visible"
            v-model="password"
            v-on:keyup.enter="login"
          ></v-text-field>

          <v-card class="mb-12" color="surface-variant" variant="tonal">
            <v-card-text class="text-medium-emphasis text-caption">
              Dit geeft toegang to instellingen die de installatie kunnen
              beschadigen. Wees voorzichtig. Dit wachtwoord is gegenereerd bij
              het eerste setup van deze machine.
            </v-card-text>
          </v-card>

          <v-btn
            class="mb-8"
            color="blue"
            size="large"
            variant="tonal"
            block
            @click="login"
          >
            Inloggen
          </v-btn>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
definePageMeta({
  layout: false,
});

const loggedIn = useCookie("loggedIn");

if (loggedIn.value) {
  navigateTo("/recordings");
}

const visible = ref(false);
const password = ref("");
const error = ref(false);

async function login() {
  try {
    await $fetch(`http://${location.hostname}:5001/password`, {
      method: "POST",
      body: password.value,
    });
    error.value = false;

    loggedIn.value = true;

    navigateTo("/recordings");
  } catch (e) {
    error.value = true;
  }
}
</script>
