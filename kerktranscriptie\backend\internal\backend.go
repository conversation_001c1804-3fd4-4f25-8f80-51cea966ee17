package internal

import (
	"fmt"
	"log"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/ai/azopenai"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/nats-io/nats.go"
)

type Backend struct {
	OpenAI *OpenAI
	Nats *Nats
	Prompt *Prompt
	SettingsHolder *SettingsHolder
	Writer *Writer
	Preektekst *Preektekst
}

func NewBackend() (*Backend, error) {
	nc, err := nats.Connect("nats://nats:4222"); if err != nil {
		log.Fatal("Connection to NATS failed")
		return nil, err
	}

	log.Println("Connected to NATS")

	s, err := NewSettingsHolder(); if err != nil {
		return nil, err
	}

	log.Println("Settings initialized")

	keyCredential := azcore.NewKeyCredential(s.Settings.OpenAIApiKey)
	client, err := azopenai.NewClientForOpenAI("https://api.openai.com/v1", keyCredential, nil); if err != nil {
		return nil, err
	}

	log.Println("Connected to OpenAI")

	w, err := NewWriter(fmt.Sprintf("%d", time.Now().Unix())); if err != nil {
		return nil, err
	}

	log.Println("Started writers")

	var p *Preektekst
	if (s.Settings.PreektekstApiKey != "") {
		p = NewPreektekst(s.Settings.PreektekstApiUrl, s.Settings.PreektekstApiKey, s.Settings.PreektekstChurchID)
		err = p.LoadOrganizationData(); if err != nil {
			return nil, err
		}
		s.Settings.PreektekstChurchID = p.churchID
		s.WriteToFile()

		log.Println("Loaded Preektekst data")
	}

	b := &Backend{}
	b.SettingsHolder = s
	b.OpenAI = NewOpenAI(b, client)
	b.Writer = w
	b.Prompt = NewPrompt(b)
	b.Preektekst = p
	b.Nats, err = NewNats(b, nc); if err != nil {
		return nil, err
	}

	return b, nil
}

func (b *Backend) Close() {
	b.Writer.Close()
	b.Nats.Close()
}