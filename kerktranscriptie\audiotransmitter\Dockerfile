FROM golang:1.22-bullseye

RUN apt update && apt install portaudio19-dev -y

WORKDIR /app

COPY go.mod go.mod

RUN go mod download
RUN go mod verify

COPY . .

ENV LD_RUN_PATH=/app/audiotransmitter/onnxruntime-arm/lib
ENV LIBRARY_PATH=/app/audiotransmitter/onnxruntime-arm/lib
ENV C_INCLUDE_PATH=/app/audiotransmitter/onnxruntime-arm/include
ENV PA_ALSA_PLUGHW=1

RUN CGO_ENABLED=1 GOOS=linux GOARCH=arm64 cd audiotransmitter && go build -o /main .

CMD ["/main"]