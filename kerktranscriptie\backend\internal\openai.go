package internal

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/ai/azopenai"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
)

type OpenAI struct {
	openAiClient *azopenai.Client
	backend *Backend
}

func NewOpenAI(b *Backend, client *azopenai.Client) *OpenAI {
	return &OpenAI{
		openAiClient: client,
		backend: b,
	}
}

func (o *OpenAI) handlePrompt(p Prompt) string {
	fmt.Printf("Transcribing %d seconds\n", int(len(p.buf)/16000))
	err := o.transcribe(&p); if err != nil {
		fmt.Printf("Transcribing failed: %s\n", err)
		return ""
	}

	txt, err := o.completeTranscription(&p); if err != nil {
		fmt.Printf("Completing transcription failed: %s\n", err)
		return ""
	}

	return txt
}

func (o *OpenAI) transcribe(p *Prompt) error {
	output, err := p.toWav(); if err != nil {
		fmt.Println(err)
		return err
	}

	// os.WriteFile(fmt.Sprintf("audio/%d.wav", time.Now().UnixNano()), output, 0644)

	res, err := o.uploadBuffer(output, p.toString(o.backend.SettingsHolder.Settings.CommonWords), 5); if err != nil {
		return err
	}

	lower := strings.ToLower(res)
	for _, word := range o.backend.SettingsHolder.Settings.IgnoredWords {
		if strings.Contains(lower, word) {
			return nil
		}
	}

	p.Message.Content = res

	if !o.backend.SettingsHolder.Settings.FinalTranscriptionOnly {
		o.backend.Nats.Publish(p.Message)
	}
	
	return nil
}

func (o *OpenAI) completeTranscription(p *Prompt) (string, error) {
	res, err := o.processMessage(p); if err != nil {
		return "", err
	}
	p.Message.Content = res
	p.Message.Completed = true

	o.backend.Nats.Publish(p.Message)

	return res, nil
}

func (o *OpenAI) uploadBuffer(buf []byte, text string, timeout int) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout) * time.Second)
	defer cancel()

	options := azopenai.AudioTranscriptionOptions{
        File: buf,
        Filename: to.Ptr("audio.wav"),
        ResponseFormat: to.Ptr(azopenai.AudioTranscriptionFormatText),
        Language: &o.backend.SettingsHolder.Settings.Language,
        DeploymentName: to.Ptr(o.backend.SettingsHolder.Settings.WhisperVersion),
    }
    
    // Only set the prompt if WhisperVersion is "whisper-1"
    if *options.DeploymentName == "whisper-1" {
        options.Prompt = &text
    }

	resp, err := o.openAiClient.GetAudioTranscription(ctx, options, nil); if err != nil {
		return "", err
	}

	result := strings.TrimSpace(*resp.Text)
	result = strings.ReplaceAll(result, "\n", " ")

	return result, nil
}

func (o *OpenAI) processMessage(p *Prompt) (string, error) {
	messages := append(
		[]azopenai.ChatRequestMessageClassification{
			&azopenai.ChatRequestSystemMessage{
				Content: azopenai.NewChatRequestSystemMessageContent(o.backend.SettingsHolder.Settings.PostProcessingPrompt),
			},
		}, p.messages...
	)

	userMessage := &azopenai.ChatRequestUserMessage{
		Content: azopenai.NewChatRequestUserMessageContent(p.Message.Content),
	}

	messages = append(messages, userMessage)

	resp, err := o.openAiClient.GetChatCompletions(context.TODO(), azopenai.ChatCompletionsOptions{
		Messages: messages,
		DeploymentName: to.Ptr(o.backend.SettingsHolder.Settings.ChatGPTVersion),
	}, nil); if err != nil {
		return "", err
	}

	var result string

	for _, choice := range resp.Choices {
		result = *choice.Message.Content
	}

	responseMessage := &azopenai.ChatRequestAssistantMessage{
		Content: azopenai.NewChatRequestAssistantMessageContent(result),
	}
	p.messages = append(p.messages, []azopenai.ChatRequestMessageClassification{userMessage, responseMessage}...)

	if len(p.messages) > 10 {
		p.messages = p.messages[2:]
	}

	return result, nil
}

func (o *OpenAI) Translate(text string, language string) (string, error) {
	messages := []azopenai.ChatRequestMessageClassification{
		&azopenai.ChatRequestSystemMessage{
			Content: azopenai.NewChatRequestSystemMessageContent(fmt.Sprintf("You are a translator, translating %s to %s. Translate every message that comes in.", o.backend.SettingsHolder.Settings.Language, language)),
		},
		&azopenai.ChatRequestUserMessage{
			Content: azopenai.NewChatRequestUserMessageContent(text),
		},
	}
	resp, err := o.openAiClient.GetChatCompletions(context.TODO(), azopenai.ChatCompletionsOptions{
		Messages: messages,
		DeploymentName: to.Ptr(o.backend.SettingsHolder.Settings.ChatGPTVersion),
	}, nil); if err != nil {
		return "", err
	}

	result := ""

	for _, choice := range resp.Choices {
		result = *choice.Message.Content
	}

	return result, nil
}

