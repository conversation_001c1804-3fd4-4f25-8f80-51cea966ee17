package internal

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"
)

type Preektekst struct {
	baseURL string
	accessKey string
	organizationID uint
	churchID uint
}

func NewPreektekst(baseURL, accessKey string, churchID uint) *Preektekst {
	return &Preektekst{
		baseURL: baseURL,
		accessKey: accessKey,
		churchID: churchID,
	}
}

type Church struct {
	ID 		       uint `json:"id"`
	Name    	   string `json:"name"`
	City     	   string `json:"city"`
	Street		   string `json:"street"`
	Number  	   int    `json:"number"`
	ZipCode		   string `json:"zip_code"`
	Country 	   string `json:"country"`
	Online		   bool   `json:"online"`
	Settings       *Settings `json:"settings,omitempty"`
	ImagePath      string `json:"image_path"`
	OrganizationID uint   `json:"organization_id"`
}

type Organization struct {
	ID 		       uint `json:"id"`
	ZitadelId	   string `json:"zitadel_id"`
	ZitadelAdmin   bool   `json:"zitadel_admin"`
	Name    	   string `json:"name"`
	City     	   string `json:"city"`
	Street		   string `json:"street"`
	Number  	   string `json:"number"`
	Country 	   string `json:"country"`
	Churches 	   []Church `json:"churches" gorm:"foreignKey:OrganizationID;constraint:OnDelete:CASCADE;"`
}

type User struct {
	ZitadelId      string `json:"zitadelId"`
	OrganizationID uint `json:"organizationId"`
	Organization   *Organization `json:"organization,omitempty"`
	Username       string `json:"userName"`
	Email          string `json:"email"`
	GlobalAdmin    bool   `json:"globalAdmin"`
	Roles	       map[string][]string `json:"roles"`
}

func (p *Preektekst) LoadOrganizationData() error {
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/users/me", p.baseURL), nil); if err != nil {
		return err
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", p.accessKey))

	client := http.Client{}
	resp, err := client.Do(req); if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("organization data retrieval failed: %s", resp.Status)
	}

	var user User
	err = json.NewDecoder(resp.Body).Decode(&user); if err != nil {
		return err
	}

	p.organizationID = user.OrganizationID
	if len(user.Organization.Churches) > 0 && p.churchID == 0 {
		p.churchID = user.Organization.Churches[0].ID
	}

	return nil
}

type Transcription struct {
	ID 		      uint   `json:"id"`
	Name          string `json:"name"`
	Language      string `json:"language"`
	ChurchID      uint   `json:"church_id"`
}

func (p *Preektekst) CreateNewTranscription(filename, language string) (uint, error) {
	name, err := formatTranscriptionName(filename); if err != nil {
		return 0, err
	}
	data := Transcription{
		Name: name,
		Language: language,
		ChurchID: p.churchID,
	}

	jsonData, err := json.Marshal(data); if err != nil {
		return 0, err
	}

	req, err := http.NewRequest("POST", fmt.Sprintf("%s/transcriptions", p.baseURL), bytes.NewBuffer(jsonData)); if err != nil {
		return 0, err
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", p.accessKey))
	req.Header.Set("Content-Type", "application/json")

	client := http.Client{}
	resp, err := client.Do(req); if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return 0, fmt.Errorf("transcription creation failed: %s", resp.Status)
	}

	var t Transcription
	err = json.NewDecoder(resp.Body).Decode(&t); if err != nil {
		return 0, err
	}

	return t.ID, nil
}

func (p *Preektekst) UploadTranscriptionAudio(tID uint, filepath string) error {
	data, err := os.OpenFile(filepath, os.O_RDONLY, 0644); if err != nil {
		return err
	}
	req, err := http.NewRequest("PUT", fmt.Sprintf("%s/transcriptions/%d/audio", p.baseURL, tID), data); if err != nil {
		return err
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", p.accessKey))
	req.Header.Set("Content-Type", "application/octet-stream")

	client := http.Client{}
	resp, err := client.Do(req); if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= http.StatusBadRequest {
		data, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed: %s, %v", resp.Status, string(data))
	}

	return nil
}


func formatTranscriptionName(epoch string) (string, error) {
	unixInt, err := strconv.ParseInt(epoch, 10, 64)
	if err != nil {
		fmt.Println("Fout bij parsen:", err)
		return "", err
	}

	t := time.Unix(unixInt, 0)

	formattedTime := t.Format("2006-01-02 15:04")

	return formattedTime, nil
}

