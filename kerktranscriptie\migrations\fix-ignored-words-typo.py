settings_file = '/opt/kerktranscriptie/.env.local'

settings = {}

with open(settings_file) as f_in:
    for line in f_in:
        lineSplitted = line.split('=')
        settings[lineSplitted[0]] = lineSplitted[1]


common_words = settings['COMMON_WORDS']
ignored_words = settings['IGNORED_WORDS']

if 'amara.org' in common_words:
    settings['COMMON_WORDS'] = ignored_words
    settings['IGNORED_WORDS'] = common_words

with open(settings_file, 'w') as f_out:
    for setting in settings:
        f_out.write(f"{setting}={settings[setting]}")