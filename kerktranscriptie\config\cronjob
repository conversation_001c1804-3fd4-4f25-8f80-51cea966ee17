@reboot sleep 30 && /opt/kerktranscriptie/update.sh

# Remove files older than 30 days
@reboot find /opt/kerktranscriptie/audio/backend/*.tmp -mtime +30 -exec rm {} \;
@reboot find /opt/kerktranscriptie/audio/backend/*.ogg -mtime +30 -exec rm {} \;

# Remove files older than 1 day and smaller than 200k
@reboot find /opt/kerktranscriptie/audio/backend/*.tmp -mtime +1 -size -200k -exec rm {} \;
@reboot find /opt/kerktranscriptie/audio/backend/*.ogg -mtime +1 -size -200k -exec rm {} \;
