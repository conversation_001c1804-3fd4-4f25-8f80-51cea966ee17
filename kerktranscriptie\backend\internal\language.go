package internal

import (
	"encoding/json"
	"os"
)

type Language struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

func LoadLanguages(filepath string) (map[string]Language, error) {
	data, err := os.ReadFile(filepath); if err != nil {
		return nil, err
	}

	var languages []Language
	err = json.Unmarshal(data, &languages); if err != nil {
		return nil, err
	}

	langMap := make(map[string]Language)
	for _, lang := range languages {
		langMap[lang.Code] = lang
	}
	
	return langMap, nil
}