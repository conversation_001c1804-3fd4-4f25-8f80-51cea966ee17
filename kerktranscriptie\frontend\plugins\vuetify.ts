// import this after install `@mdi/font` package
import "@mdi/font/css/materialdesignicons.css";

import "vuetify/styles";
import { createVuetify } from "vuetify";
import { nl } from "vuetify/locale";

export default defineNuxtPlugin((app) => {
  const vuetify = createVuetify({
    theme: {
      defaultTheme: "dark",
    },
    locale: {
      locale: "nl",
      fallback: "en",
      messages: {
        nl,
      },
    },
    ssr: true,
  });
  app.vueApp.use(vuetify);
});
