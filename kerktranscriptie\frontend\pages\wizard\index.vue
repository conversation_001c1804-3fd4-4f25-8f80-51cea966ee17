<template>
  <v-dialog
    max-width="1000"
    v-model="openDialog"
    v-if="!settingsStore.settings['setupFinished']"
  >
    <v-alert
      title="Opslaan mislukt"
      type="error"
      :text="errorMessage"
      v-if="errorMessage"
    ></v-alert>
    <v-stepper
      :items="['Wachtwoord', 'Audio device', 'Transcriptie']"
      :hide-actions="selected === 3"
      :next-text="selected === 3 ? 'Verzenden' : 'Volgende'"
      v-model="selected"
      min-height="50vh"
      class="d-flex flex-column"
    >
      <template v-slot:item.1>
        <v-form @submit.prevent>
          <v-container fluid>
            <v-card-text class="text-caption">
              Stel hier het admin wachtwoord in. Onthoud dit wachtwoord goed en
              zorg ervoor dat het een veilig wachtwoord is.
            </v-card-text>
            <v-text-field
              v-model="password"
              :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
              :rules="[rules.required, rules.min]"
              :type="showPassword ? 'text' : 'password'"
              hint="Minstens 8 karakters"
              name="password"
              counter
              @click:append="showPassword = !showPassword"
            ></v-text-field>
          </v-container>
        </v-form>
      </template>
      <template v-slot:item.2>
        <v-form @submit.prevent>
          <v-container fluid>
            <v-card-text class="text-caption">
              Kies hier welk audio apparaat de input moet leveren voor de
              transcriptie.
            </v-card-text>
            <v-autocomplete
              label="Audio"
              :items="devices"
              v-model="device"
              :menu-props="{ eager: true }"
            ></v-autocomplete>
            <v-card-text class="text-caption">
              Minimaal volume van de microfoon voordat er spraak opgenomen wordt
            </v-card-text>
            <v-slider
              v-model="minMicVolume"
              :max="1000"
              :min="0"
              :step="1"
              class="align-center"
              hide-details
            >
              <template v-slot:append>
                <v-text-field
                  v-model="minMicVolume"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  hide-details
                  single-line
                ></v-text-field>
              </template>
            </v-slider>
          </v-container>
        </v-form>
      </template>
      <template v-slot:item.3>
        <v-form @submit.prevent>
          <v-container fluid :scrollable="true">
            <v-card-text class="text-medium-emphasis text-caption">
              API key
            </v-card-text>
            <v-text-field v-model="apiKey" name="API Key"></v-text-field>
            <v-card-text class="text-medium-emphasis text-caption">
              Taal van de preek
            </v-card-text>
            <v-autocomplete
              label="Talen"
              :items="languages"
              item-title="name"
              item-value="code"
              v-model="language"
              :menu-props="{ eager: true }"
            ></v-autocomplete>

            <v-card-text class="text-medium-emphasis text-caption">
              Minimale en maximale tijd van een tekstfragment
            </v-card-text>
            <v-range-slider
              v-model="minAndMaxSeconds"
              strict
              :min="0"
              :max="30"
              :step="1"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="minAndMaxSeconds[0]"
                  density="compact"
                  style="width: 70px"
                  type="number"
                  variant="outlined"
                  hide-details
                  single-line
                ></v-text-field>
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="minAndMaxSeconds[1]"
                  density="compact"
                  style="width: 70px"
                  type="number"
                  variant="outlined"
                  hide-details
                  single-line
                ></v-text-field> </template
            ></v-range-slider>
            <v-card-text class="text-medium-emphasis text-caption">
              Behulpzame woorden die de tekst verbeteren, gescheiden door een
              komma
            </v-card-text>
            <v-combobox
              label="Veel voorkomende woorden"
              v-model="commonWords"
              chips
              multiple
            ></v-combobox>
            <v-card-text class="text-medium-emphasis text-caption">
              Wanneer deze woorden in een zin staan, wordt de hele zin
              verwijderd
            </v-card-text>
            <v-combobox
              label="Genegeerde woorden"
              v-model="ignoredWords"
              chips
              multiple
            ></v-combobox>
          </v-container>
        </v-form>
      </template>

      <v-spacer></v-spacer>

      <v-stepper-actions
        v-if="selected === 3"
        :disabled="false"
        @click:next="send()"
        @click:prev="selected = selected - 1"
      ></v-stepper-actions>
    </v-stepper>
  </v-dialog>
</template>

<script setup>
import languages from "@/assets/languages.json";

const settingsStore = useSettingsStore();

if (settingsStore.settings["setupFinished"]) {
  navigateTo("/settings");
}

const openDialog = true;

const selected = ref(false);

const password = ref("");
const device = ref("");
const minMicVolume = ref(200);
const apiKey = ref(settingsStore.settings["openAIApiKey"]);
const language = ref(settingsStore.settings["language"]);
const commonWords = ref(settingsStore.settings["commonWords"]);
const ignoredWords = ref(settingsStore.settings["ignoredWords"]);
const minAndMaxSeconds = ref([3, 15]);

const showPassword = ref(false);
const rules = {
  required: (value) => !!value || "Required.",
  min: (v) => v.length >= 8 || "Min 8 characters",
};

const devices = await $fetch(`http://${location.hostname}:5001/devices`);

const errorMessage = ref("");

async function send() {
  try {
    await $fetch(`http://${location.hostname}:5001/password`, {
      method: "PUT",
      body: password.value,
    });

    await $fetch(`http://${location.hostname}:5001/settings`, {
      method: "PATCH",
      body: {
        alsaInput: device.value,
        minMicVolume: minMicVolume.value,
        openAIApiKey: apiKey.value,
        language: language.value,
        commonWords: commonWords.value,
        ignoredWords: ignoredWords.value,
        minStepS: minAndMaxSeconds.value[0],
        maxStepS: minAndMaxSeconds.value[1],
      },
    });
    navigateTo("/");
  } catch (e) {
    errorMessage.value = e.data;
  }
}
</script>
