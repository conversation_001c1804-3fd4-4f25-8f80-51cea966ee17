package filter

import (
	"fmt"
	"log"
	"time"

	vadlib "github.com/WesotronicAV/kerktranscriptie/audiotransmitter/vad"
	"github.com/go-audio/audio"
)

type SileroFilter struct {
	silero *vadlib.SileroDetector
}

func NewSileroFilter() *SileroFilter {
	// Silero VAD - pre-trained Voice Activity Detector. See: https://github.com/snakers4/silero-vad
	sileroFilePath := "files/silero_vad.onnx"
	silero, err := vadlib.NewSileroDetector(sileroFilePath)
	if err != nil {
		log.Fatalf("creating silero detector: %v", err)
	}
	return &SileroFilter{silero}
}

func (f *SileroFilter) Apply(input *audio.PCMBuffer) *audio.PCMBuffer {
	start := time.Now()
	detected, err := f.silero.DetectVoice(input)
	if err != nil {
		log.Println(fmt.Errorf("detect voice: %w", err))
		return nil
	}
	log.Println("voice detecting result", time.Since(start), detected)

	if detected {
		log.Println("SileroFilter: contains voice, sending through...")
		return input
	}
	log.Println("SileroFilter: no voice detected, skipping...")
	return nil
}