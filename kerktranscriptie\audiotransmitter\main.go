package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/go-audio/audio"
	"github.com/gordonklaus/portaudio"
	"github.com/nats-io/nats.go"

	"github.com/WesotronicAV/kerktranscriptie/audiotransmitter/filter"
	"github.com/WesotronicAV/kerktranscriptie/audiotransmitter/utils"
	"github.com/WesotronicAV/kerktranscriptie/common"
)

func main() {
	portaudio.Initialize()
	defer portaudio.Terminate()

	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	config, err := utils.NewConfig("/app/.env.local"); if err != nil {
		log.Fatal(err)
	}

	config.PrintConfig()

	nc, err := nats.Connect("nats://nats:4222"); if err != nil {
		log.Fatal("Connection to NATS failed")
	}
	defer nc.Drain()

	restartOnSettingsChange()

	done := make(chan bool)
	audioCtx, audioCancel := context.WithCancel(ctx)

	// Set up the audio stream parameters for LINEAR16 PCM
	in := make([]int16, 4096) // Use int16 to capture 16-bit samples.

	params := portaudio.StreamParameters{
		Input: portaudio.StreamDeviceParameters{
			Device: config.Microphone,
			Channels: 2,
			Latency: config.Microphone.DefaultHighInputLatency,
		},
		SampleRate: 16000,
		FramesPerBuffer: len(in),
	}

	audioStream, err := portaudio.OpenStream(params, &in)
	if err != nil {
		log.Fatalf("opening stream: %v", err)
		return
	}

	// Start the audio stream
	if err := audioStream.Start(); err != nil {
		log.Fatalf("starting stream: %v", err)
		return
	}

	log.Println("started")

	var (
		processChan    = make(chan []int16, 10)
		fileChan       = make(chan audio.Buffer, 10)
	)

	filters := []filter.Filter{
		filter.NewVolumeFilter(config.MinimumSegmentVolumeDb),
	}

	if config.EnableVad {
		sileroFilter := filter.NewSileroFilter()
		filters = append(filters, sileroFilter)
	}

	if config.DevMode {
		fileFilter := filter.NewFileFilter(fmt.Sprintf("%d", time.Now().Unix()))
		filters = append(filters, fileFilter)
	}

  	// Listening to the audiostream
	go listen(audioCtx, audioStream, config, in, processChan)
	go process(processChan, filters, fileChan, nc, config.AudioTransmitterToken)

	// Shutdown.
	go func() {
		<-ctx.Done()
		log.Println("closing")
		if err := ctx.Err(); err != nil {
			log.Println(fmt.Errorf("shutdown: %w", err))
		}
		close(fileChan)
		audioCancel()
		time.Sleep(2*time.Second)
		close(done)
	}()

	<-done
	log.Println("finished")
}

func restartOnSettingsChange() {
	watcher, err := fsnotify.NewWatcher(); if err != nil {
		log.Fatal(err)
	}

	go func() {
		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}
				if event.Has(fsnotify.Write) {
					os.Exit(0)
				}
			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				log.Println("error:", err)
			}
		}
	}()

	err = watcher.Add("/app/.env.local"); if err != nil {
		log.Fatal(err)
	}
}

func listen(audioCtx context.Context, audioStream *portaudio.Stream, config *utils.Config, in []int16, processChan chan []int16) {
  var startListening time.Time
  listening := false

  buffer := make([]int16, 0)

  for {
    select {
    case <-audioCtx.Done():
      if err := audioStream.Close(); err != nil {
        log.Println(err)
      }
      log.Println("got audioCtx.Done exit gracefully...")
      return
    default:
      // Read from the microphone
      if err := audioStream.Read(); err != nil {
        log.Printf("reading from stream: %v\n", err)
        continue
      }

      volume := utils.CalculateRMS16(in)
      if volume > config.MinimumVolume && !listening {
        listening = true
        startListening = time.Now()
        log.Println("Start listening...", volume, startListening, config.MinimumSegment, config.MaximumSegment)
      }

      if !listening {
        continue
      }

      buffer = append(buffer, in...)

      if (volume < config.MinimumVolume && time.Since(startListening) > config.MinimumSegment) || time.Since(startListening) > config.MaximumSegment {
        log.Println("Stop listening...", volume, startListening, config.MinimumSegment, config.MaximumSegment)
        listening = false

		copiedBuffer := make([]int16, len(buffer))
		copy(copiedBuffer, buffer)

		processChan <- copiedBuffer
		buffer = make([]int16, 0)
	  }
    }
  }
}

func process(input <-chan []int16, filters []filter.Filter, output chan audio.Buffer, nc *nats.Conn, token string) {
	for data := range input {
		soundBuffer := &audio.PCMBuffer{
			Format: &audio.Format{SampleRate: 16000, NumChannels: 2},
			I16: data,
			DataType: audio.DataTypeI16,
		}

		for _, filter := range filters {
			soundBuffer = filter.Apply(soundBuffer)
			if soundBuffer == nil {
				break
			}
		}
		if soundBuffer != nil {
			body := common.ConvertInt16ToByteSlice(data)

			err := nc.Publish(token, body); if err != nil {
				fmt.Println(err)
			}
		}		
	}
}