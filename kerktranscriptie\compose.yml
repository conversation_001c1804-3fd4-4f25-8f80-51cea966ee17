services:
  frontend:
    build:
      context: ./
      dockerfile: frontend/Dockerfile
    restart: unless-stopped
    depends_on: ["nats"]
    ports:
      - "${FRONTEND_PORT}:80"
  backend:
    build:
      context: ./
      dockerfile: ./backend/Dockerfile
    restart: unless-stopped
    depends_on: ["nats"]
    ports:
      - "${BACKEND_PORT}:${BACKEND_PORT}"
    env_file:
      - path: ./.env
        required: true # default
      - path: ./.env.local
        required: false
    devices:
      - /dev/snd:/dev/snd
    volumes:
      - ./.env:/app/.env
      - ./.env.local:/app/.env.local
      - ./audio/backend:/app/audio
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "-O",
          "/dev/null",
          "http://localhost:${BACKEND_PORT}/health",
        ]
      interval: 1s
      timeout: 1s
      retries: 5
      start_period: 1s
  nats:
    image: "nats:alpine"
    restart: unless-stopped
    ports:
      - "${NATS_PORT}:4222" # Default NATS port
      - "8222:8222" # Web interface port
      - "${NATS_WS_PORT}:${NATS_WS_PORT}" # WS port
    volumes:
      - ./mq:/container
    command: --http_port 8222 -c /container/nats.conf
    healthcheck:
      test: wget http://localhost:8222/healthz -q -S -O -
      start_period: 3s
      retries: 3
      timeout: 3s
      interval: 14s
    env_file:
      - path: ./.env
        required: true # default
      - path: ./.env.local
        required: false
  audiotransmitter:
    build:
      context: ./
      dockerfile: ./audiotransmitter/Dockerfile
    restart: unless-stopped
    depends_on:
      nats:
        condition: service_healthy
    devices:
      - /dev/snd:/dev/snd
    env_file:
      - path: ./.env
        required: true # default
      - path: ./.env.local
        required: false
    volumes:
      - ./audiotransmitter/files:/app/files
      - ./.env.local:/app/.env.local
      - ./audio/transmitter:/app/audio
