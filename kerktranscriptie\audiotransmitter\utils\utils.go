package utils

import "math"

func ConvertInt16ToInt(input []int16) []int {
	output := make([]int, len(input)) // Allocate a slice for the output
	for i, value := range input {
		output[i] = int(value) // Convert each int16 to int and assign it to the output slice
	}
	return output // Return the converted slice
}

// calculateRMS16 calculates the root mean square of the audio buffer for int16 samples.
func CalculateRMS16(buffer []int16) float64 {
	var sumSquares float64
	for _, sample := range buffer {
		val := float64(sample) // Convert int16 to float64 for calculation
		sumSquares += val * val
	}
	meanSquares := sumSquares / float64(len(buffer))
	return math.Sqrt(meanSquares)
}

func CalculateDecibel(rms float64) float64 {
	return 20 * math.Log10(rms)
}
