<template>
  <v-container>
    <v-dialog v-model="errorDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h5">Foutmelding</v-card-title>
        <v-card-text>
          <v-alert type="error" variant="tonal">
            {{ errorMessage }}
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="errorDialog = false">Sluiten</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-snackbar v-model="successSnackbar" timeout="3000" color="success">
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn variant="text" @click="successSnackbar = false">Sluiten</v-btn>
      </template>
    </v-snackbar>
    <v-table>
      <thead>
        <tr>
          <th class="text-left">Starttijd</th>
          <th class="text-left">Grootte</th>
          <th class="text-right">Acties</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="file in files" :key="file.path">
          <td>{{ file.readableName }}</td>
          <td>{{ formatSize(file.size) }}</td>
          <td class="text-right py-2">
            <div v-if="file.path.endsWith('.tmp')">
              <div v-if="loadingFiles[file.path]">
                <v-progress-linear
                  color="primary"
                  :model-value="progressValues[file.path] || 0"
                  height="10"
                  class="mb-2"
                ></v-progress-linear>
                <div class="text-caption text-center mb-2">
                  Bezig met verzenden naar Preeklezen... {{ Math.round(progressValues[file.path] || 0) }}%
                </div>
              </div>
              <v-btn
                v-else
                color="primary"
                class="mr-2"
                @click="uploadFile(file)"
                :disabled="loadingFiles[file.path]"
              >
                <v-icon class="mr-1">mdi-upload</v-icon>
                <span class="d-none d-sm-inline">Opsturen naar </span>
                <span>Preeklezen</span>
              </v-btn>
            </div>

            <v-btn icon color="error" @click="deleteFile(file)" :disabled="loadingFiles[file.path]">
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </td>
        </tr>
      </tbody>
    </v-table>
  </v-container>
</template>

<script setup>
definePageMeta({
  title: "Opnames",
  layout: "secure",
});

const errorDialog = ref(false);
const errorMessage = ref("Er is een onverwachte fout opgetreden.");

const successSnackbar = ref(false);
const successMessage = ref("De actie is succesvol uitgevoerd!");

const files = ref(await $fetch(`http://${location.hostname}:5001/recordings`));
const loadingFiles = ref({});
const progressValues = ref({});
const progressIntervals = ref({});

const formatSize = (bytes) => {
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  if (bytes === 0) return "0 Bytes";
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return (bytes / Math.pow(1024, i)).toFixed(2) + " " + sizes[i];
};

const fileUrl = (file) => {
  return `http://${location.hostname}:5001/recordings/${file.path}`;
};

// Calculate estimated upload time based on file size (in MB)
const calculateEstimatedTime = (sizeInBytes) => {
  const sizeInMB = sizeInBytes / (1024 * 1024);

  // Based on the information provided:
  // 200MB takes ~90 seconds
  // 270MB takes ~120 seconds
  // This gives us a rough formula: time = 0.43 * size + 4
  const estimatedSeconds = 0.43 * sizeInMB + 4;

  // Return time in milliseconds, with a minimum of 10 seconds
  return Math.max(estimatedSeconds, 10) * 1000;
};

const startProgressTimer = (file) => {
  // Reset progress
  progressValues.value = { ...progressValues.value, [file.path]: 0 };

  // Calculate total estimated time based on file size
  const totalTime = calculateEstimatedTime(file.size);
  const startTime = Date.now();
  const endTime = startTime + totalTime;

  // Clear any existing interval
  if (progressIntervals.value[file.path]) {
    clearInterval(progressIntervals.value[file.path]);
  }

  // Create a new interval that updates every 100ms
  progressIntervals.value[file.path] = setInterval(() => {
    const now = Date.now();

    if (now >= endTime) {
      // We've reached the estimated end time, but the request might still be processing
      // Cap at 95% until we get confirmation of completion
      progressValues.value = { ...progressValues.value, [file.path]: 95 };
    } else {
      // Calculate progress percentage
      const elapsed = now - startTime;
      const progress = (elapsed / totalTime) * 100;
      progressValues.value = { ...progressValues.value, [file.path]: Math.min(progress, 95) };
    }
  }, 100);
};

const stopProgressTimer = (file) => {
  if (progressIntervals.value[file.path]) {
    clearInterval(progressIntervals.value[file.path]);
    delete progressIntervals.value[file.path];
  }

  // Set to 100% when complete
  progressValues.value = { ...progressValues.value, [file.path]: 100 };

  // Clear progress after a short delay
  setTimeout(() => {
    const newProgressValues = { ...progressValues.value };
    delete newProgressValues[file.path];
    progressValues.value = newProgressValues;
  }, 500);
};

const uploadFile = async (file) => {
  try {
    // Set loading state for this specific file
    loadingFiles.value = { ...loadingFiles.value, [file.path]: true };

    // Start the progress timer
    startProgressTimer(file);

    // Make the API call
    await $fetch(`${fileUrl(file)}/preektekst`, { method: "PUT" });

    // Show success message
    successSnackbar.value = true;
    successMessage.value =
      "De transcriptie is succesvol aangemaakt bij Preeklezen!";

    // Refresh the file list
    files.value = await $fetch(`http://${location.hostname}:5001/recordings`);
  } catch (error) {
    // Show error message
    errorMessage.value = error.data;
    errorDialog.value = true;
  } finally {
    // Stop the progress timer
    stopProgressTimer(file);

    // Clear loading state
    loadingFiles.value = { ...loadingFiles.value, [file.path]: false };
  }
};

const deleteFile = async (file) => {
  // Don't allow deletion while uploading
  if (loadingFiles.value[file.path]) {
    return;
  }

  await $fetch(fileUrl(file), { method: "DELETE" });
  files.value = await $fetch(`http://${location.hostname}:5001/recordings`);
};

// Clean up all intervals when component is unmounted
onBeforeUnmount(() => {
  // Clear all progress intervals
  Object.values(progressIntervals.value).forEach(interval => {
    clearInterval(interval);
  });
});
</script>
