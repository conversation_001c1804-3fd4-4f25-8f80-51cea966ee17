package common

import "encoding/binary"

func ConvertInt16ToByteSlice(s []int16) []byte {
	b := make([]byte, len(s)*2)
	for i, v := range s {
		binary.LittleEndian.PutUint16(b[i*2:], uint16(v))
	}
	return b
}

func ConvertByteSliceToInt16(b []byte) []int16 {
	if len(b)%2 != 0 {
		panic("invalid byte slice length, must be multiple of 2")
	}

	s := make([]int16, len(b)/2)
	for i := range s {
		s[i] = int16(binary.LittleEndian.Uint16(b[i*2:]))
	}
	return s
}