{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@mdi/font": "^7.4.47", "@pinia/nuxt": "^0.5.1", "nats.ws": "^1.23.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"nuxt": "^3.13.2", "vite-plugin-vuetify": "^2.0.3", "vuetify": "^3.7.2"}}