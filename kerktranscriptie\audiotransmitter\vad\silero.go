package vad

import (
	"fmt"
	"log"

	"github.com/go-audio/audio"
	"github.com/streamer45/silero-vad-go/speech"
)

type SileroDetector struct {
	detector *speech.Detector
}

func NewSileroDetector(filepath string) (*SileroDetector, error) {
	sd, err := speech.NewDetector(speech.DetectorConfig{
		ModelPath:            filepath,
		SampleRate:           16000,
		WindowSize:           1024,
		Threshold:            0.3,
		MinSilenceDurationMs: 0,
		SpeechPadMs:          0,
	})
	if err != nil {
		return nil, fmt.Errorf("create silero detector: %w", err)
	}

	return &SileroDetector{
		detector: sd,
	}, nil
}

func (s *SileroDetector) DetectVoice(buffer *audio.PCMBuffer) (bool, error) {
	segments, err := s.detector.Detect(buffer.AsF32())
	if err != nil {
		log.Println(fmt.Errorf("detect voice: %w", err))
		return true, nil
	}

	return len(segments) > 0, nil
}
