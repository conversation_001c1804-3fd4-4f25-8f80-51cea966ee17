package internal

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gordonklaus/portaudio"
	"github.com/rs/cors"
	"golang.org/x/crypto/bcrypt"
)

type SubscriptionRequest struct {
	Language string `json:"language"`
}

func ServeApi(b *Backend) error {
	mux := http.NewServeMux()

	mux.HandleFunc("/subscriptions", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "POST" {
			var t SubscriptionRequest
			err := json.NewDecoder(r.Body).Decode(&t); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not read body: %s\n", err)))
				return
			}

			sub, err := b.Nats.AddListener(t.Language); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not add listener: %s\n", err)))
				return
			}
			w.Write([]byte(sub))
		}
	})

	mux.HandleFunc("/settings", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		if r.Method == "GET" {
			err := json.NewEncoder(w).Encode(b.SettingsHolder.Settings); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not read settings: %s\n", err)))
				return
			}
		}

		if r.Method == "PATCH" {
			err := json.NewDecoder(r.Body).Decode(b.SettingsHolder.Settings); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not read body: %s\n", err)))
				return
			}

			err = b.SettingsHolder.WriteToFile(); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not save settings: %s\n", err)))
				return
			}

			err = json.NewEncoder(w).Encode(b.SettingsHolder.Settings); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not read settings: %s\n", err)))
				return
			}
		}
	})

	mux.HandleFunc("/devices", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		devices, err := portaudio.Devices(); if err != nil {
			w.WriteHeader(500)
			w.Write([]byte(fmt.Sprintf("could not get devices: %s\n", err)))
			return
		}

		var deviceList []string
		for _, device := range devices {
			deviceList = append(deviceList, device.Name)
		}

		err = json.NewEncoder(w).Encode(deviceList); if err != nil {
			w.WriteHeader(500)
			w.Write([]byte(fmt.Sprintf("could not encode devices: %s\n", err)))
			return
		}
	})

	mux.HandleFunc("/recordings", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		if r.Method == "GET" {
			files, err := os.ReadDir("audio"); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not read directory: %s\n", err)))
				return
			}

			type Recording struct {
				Path         string `json:"path"`
				ReadableName string `json:"readableName"`
				Size         int64  `json:"size"`
				Timestamp    int64  `json:"timestamp"` // Added for sorting
			}

			var fileList []Recording
			// Define minimum file size (150MB in bytes)
			const minFileSize int64 = 150 * 1024 * 1024

			for _, file := range files {
				if file.Name() == "README.md" || strings.HasSuffix(file.Name(), ".temp") {
					continue
				}

				fileInfo, err := file.Info(); if err != nil {
					w.WriteHeader(500)
					w.Write([]byte(fmt.Sprintf("could not get file info: %s\n", err)))
					return
				}

				// Skip files smaller than 150MB
				if fileInfo.Size() < minFileSize {
					continue
				}

				timestamp, err := strconv.ParseInt(strings.Split(file.Name(), ".")[0], 10, 64); if err != nil {
					w.WriteHeader(500)
					w.Write([]byte(fmt.Sprintf("could not parse file name: %s\n", err)))
					return
				}

				fileList = append(fileList, Recording{
					Path:         file.Name(),
					ReadableName: time.Unix(timestamp, 0).Format("02-01-2006 15:04:05"),
					Size:         fileInfo.Size(),
					Timestamp:    timestamp, // Store timestamp for sorting
				})
			}

			// Sort fileList by timestamp in descending order (newest first)
			sort.Slice(fileList, func(i, j int) bool {
				return fileList[i].Timestamp > fileList[j].Timestamp
			})

			err = json.NewEncoder(w).Encode(fileList); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not encode files: %s\n", err)))
				return
			}
		}

		if r.Method == "DELETE" {
			err := os.RemoveAll("audio"); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not delete directory: %s\n", err)))
				return
			}
		}
	})

	mux.HandleFunc("/recordings/", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "GET" {
			path := r.URL.Path[len("/recordings/"):]
			file, err := os.Open(fmt.Sprintf("audio/%s", path)); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not open file: %s\n", err)))
				return
			}
			defer file.Close()

			_, err = io.Copy(w, file); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not copy file: %s\n", err)))
				return
			}
		}

		if r.Method == "PUT" {
			if (b.Preektekst == nil) {
				w.WriteHeader(400)
				w.Write([]byte("Preektekst not configured\n"))
				return
			}

			path := r.URL.Path[len("/recordings/"):len(r.URL.Path)-len(".tmp/preektekst")]
			inputFile := fmt.Sprintf("audio/%s.tmp", path)
			outputFile := fmt.Sprintf("audio/%s.ogg", path)
			err := b.Writer.convertToOgg(inputFile, outputFile); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not convert file: %s\n", err)))
				return
			}

			tID, err := b.Preektekst.CreateNewTranscription(path, b.SettingsHolder.Settings.Language); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not create transcription: %s\n", err)))
				os.Remove(outputFile)
				return
			}

			err = b.Preektekst.UploadTranscriptionAudio(tID, outputFile); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not upload audio: %s\n", err)))
				os.Remove(outputFile)
				return
			}

			w.WriteHeader(201)
			os.Remove(inputFile)
		}

		if r.Method == "DELETE" {
			path := r.URL.Path[len("/recordings/"):]
			err := os.Remove(fmt.Sprintf("audio/%s", path)); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not delete file: %s\n", err)))
				return
			}
		}
	})

	mux.HandleFunc("/password", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "PUT" {
			body, err := io.ReadAll(r.Body); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not read body: %s\n", err)))
				return
			}

			if (len(string(body)) < 8) {
				w.WriteHeader(400)
				w.Write([]byte("Password should be 8 characters or longer\n"))
				return
			}

			password, err := bcrypt.GenerateFromPassword(body, 14); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not encrypt password: %s\n", err)))
				return
			}

			b.SettingsHolder.Password = string(password)
		}


		if r.Method == "POST" {
			password, err := io.ReadAll(r.Body); if err != nil {
				w.WriteHeader(500)
				w.Write([]byte(fmt.Sprintf("could not read body: %s\n", err)))
				return
			}

			if bcrypt.CompareHashAndPassword([]byte(b.SettingsHolder.Password), password) != nil {
				w.WriteHeader(401)
				return
			}
		}


	})

	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(200)
	})


	c := cors.AllowAll().Handler(mux)

	log.Printf("Listening on %s", os.Getenv("BACKEND_PORT"))
	return http.ListenAndServe(fmt.Sprintf(":%s", os.Getenv("BACKEND_PORT")), c)
}
