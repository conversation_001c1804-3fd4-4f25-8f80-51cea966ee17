package main

import (
	"log"

	"github.com/WesotronicAV/kerktranscriptie/backend/internal"

	"github.com/gordonklaus/portaudio"
)

func main() {
	b, err := internal.NewBackend(); if err != nil {
		log.Fatal(err)
	}

	// Connect to NATS
	defer b.Close()

	// Connect to OpenAI
	
	log.Println("Connected to OpenAI")

	portaudio.Initialize()
	defer portaudio.Terminate()

	log.Println("Portaudio initialized")

	err = internal.ServeApi(b); if err != nil {
		log.Fatal(err)
	}
}