<template>
  <v-app-bar :elevation="2">
    <template v-slot:prepend>
      <v-app-bar-nav-icon @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
    </template>

    <v-app-bar-title>
      <div class="d-flex align-center justify-space-between">
        <p>{{ $route.meta.title }}</p>
        <v-img max-width="228" class="mx-5" src="/logo-wesotronic.svg"></v-img>
      </div>
    </v-app-bar-title>
  </v-app-bar>
  <v-navigation-drawer v-model="drawer">
    <v-list nav>
      <v-list-item
        prepend-icon="mdi-home"
        title="Home"
        value="Home"
        to="/"
        nuxt
        @click.stop="drawer = !drawer"
      ></v-list-item>
      <v-list-item
        v-if="loggedIn"
        prepend-icon="mdi-playlist-music"
        title="Opnames"
        value="Opnames"
        to="/recordings"
        nuxt
        @click.stop="drawer = !drawer"
      ></v-list-item>
      <v-list-item
        v-if="loggedIn"
        prepend-icon="mdi-cog"
        title="Instellingen"
        value="Instellingen"
        to="/settings"
        nuxt
        @click.stop="drawer = !drawer"
      ></v-list-item>
      <v-list-item
        :prepend-icon="loggedIn ? 'mdi-logout' : 'mdi-account'"
        :title="loggedIn ? 'Uitloggen' : 'Inloggen'"
        value="loggedIn ? 'Uitloggen' : 'Inloggen'"
        to="/login"
        nuxt
        @click="loggedIn = false"
        @click.stop="drawer = !drawer"
      ></v-list-item>
      <v-autocomplete
        label="Talen"
        :items="languages"
        item-title="name"
        return-object
        v-model="languageStore.language"
        :menu-props="{ eager: true }"
      ></v-autocomplete>
    </v-list>
  </v-navigation-drawer>
</template>

<script setup>
import languages from "@/assets/languages.json";

const loggedIn = useCookie("loggedIn");
const language = useCookie("language");

const languageStore = useLanguageStore();
if (language.value) {
  languageStore.language = languages.filter(
    (x) => x.code === language.value
  )[0];
}

const drawer = defineModel("drawer", { default: false });

watch(
  () => languageStore.language,
  async () => {
    language.value = languageStore.language.code;
  }
);
</script>
