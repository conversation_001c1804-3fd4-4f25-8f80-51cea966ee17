FROM golang:1.22-alpine AS base

RUN apk add portaudio-dev gcc musl-dev

WORKDIR /app

COPY go.mod go.mod

RUN go mod download
RUN go mod verify

COPY . .

RUN CGO_ENABLED=1 GOOS=linux GOARCH=arm64 cd backend && go build -o /main .

# Dockerfile.distroless
FROM alpine

WORKDIR /app

RUN apk add portaudio-dev opus ffmpeg
ENV PA_ALSA_PLUGHW=1

COPY --from=base /main .
COPY common/languages.json ./languages.json

CMD ["./main"]