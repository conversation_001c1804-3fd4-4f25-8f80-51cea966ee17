set -e

[ "$UID" -eq 0 ] || exec sudo bash "$0" "$@"

echo "$(tput bold)Installing Docker...$(tput sgr0)"

sudo apt update > /dev/null
sudo apt install -y ca-certificates curl > /dev/null
sudo install -m 0755 -d /etc/apt/keyrings > /dev/null
sudo curl -fsSL https://download.docker.com/linux/debian/gpg -o /etc/apt/keyrings/docker.asc > /dev/null
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/debian \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update > /dev/null

sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

echo "$(tput bold)Getting the source...$(tput sgr0)"
echo "Enter deploy key"

read deployKey1
read deployKey2
read deployKey3
read deployKey4
read deployKey5
read deployKey6
read deployKey7

KEY_FILE=/etc/ssh/wesotronic

cat << EOF > $KEY_FILE
$deployKey1
$deployKey2
$deployKey3
$deployKey4
$deployKey5
$deployKey6
$deployKey7
EOF

chmod 600 $KEY_FILE

cat << EOF >> /etc/ssh/ssh_config

Host github.com
  IdentityFile $KEY_FILE
EOF

echo "$(tput bold)Downloading files...$(tput sgr0)"

cd /opt
<NAME_EMAIL>:WesotronicAV/kerktranscriptie.git
cd kerktranscriptie
touch .env.local

echo "$(tput bold)Building the application...$(tput sgr0)"
sudo docker compose build

echo "$(tput bold)Installing auto-update service...$(tput sgr0)"

echo "@reboot sleep 10 && /opt/kerktranscriptie/update.sh" > mycron
echo "@reboot find /opt/kerktranscriptie/audio/*.wav -mtime +30 -exec rm {} \;" >> mycron

crontab mycron
rm mycron

sudo docker compose up -d
echo "$(tput bold)DONE!$(tput sgr0)"

